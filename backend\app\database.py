"""
Database models and connections for the Datagenius backend.

This module provides the database models and connection utilities for the Datagenius backend.
"""

import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union, Tuple

from .utils.json_utils import sanitize_json

from sqlalchemy import (
    create_engine, Column, Integer, String, Text, DateTime,
    ForeignKey, Boolean, JSON, Float, UniqueConstraint, Index,
    func
)
from sqlalchemy.orm import declarative_base, sessionmaker, relationship, Session

from . import config

# Create base class for models FIRST to avoid circular imports when models import Base
Base = declarative_base()

# Import required model schemas for type hints
from .models.data_source import FileDataSourceCreate, DatabaseDataSourceCreate, ApiDataSourceCreate, McpDataSourceCreate

# Configure logging
logger = logging.getLogger(__name__)

# Enhanced database engine configuration for Phase 2 memory leak resolution
def create_optimized_engine():
    """Create optimized database engine with enhanced connection pool."""
    connect_args = {}

    if config.DATABASE_URL.startswith("sqlite"):
        connect_args = {"check_same_thread": False}
    elif config.DATABASE_URL.startswith("postgresql"):
        # PostgreSQL optimizations
        connect_args = {
            "application_name": "datagenius",
            "options": "-c statement_timeout=30000 -c jit=off",  # 30 second timeout and disable JIT
            "connect_timeout": 10
        }

    # Enhanced connection pool configuration for Phase 2 optimization
    pool_config = {
        "pool_size": 20,           # Base pool size
        "max_overflow": 30,        # Additional connections beyond pool_size
        "pool_pre_ping": True,     # Validate connections before use
        "pool_recycle": 3600,      # Recycle connections every hour
        "pool_timeout": 30,        # Timeout for getting connection from pool
        "pool_reset_on_return": "commit",  # Reset connections on return
        "pool_logging_name": "datagenius_pool",  # Pool logging identifier
    }

    return create_engine(
        config.DATABASE_URL,
        echo=config.DATABASE_ECHO,
        connect_args=connect_args,
        **pool_config
    )

# Create optimized engine (legacy support)
engine = create_optimized_engine()

# Create session factory (legacy support)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Enhanced connection manager will be initialized separately to avoid circular imports
# The connection manager is available through app.services.connection_manager
connection_manager = None

# Import get_utc_now from utils
from .utils.db_utils import get_utc_now

# Import all model modules here so Base knows about them before create_all
# These imports are required for SQLAlchemy to register all models - do not remove
# Commented out to prevent circular imports. Model imports should be handled
# by app.models.__init__.py and subsequently imported by env.py or main app.
# from .models import admin, agent, auth, cart, chat, data_source, file, knowledge_graph, persona, provider, purchase, task  # noqa: F401
# from .models import workflow  # noqa: F401

# Database models
class User(Base):
    """Model for users."""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=True)
    first_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=True)
    hashed_password = Column(String(255), nullable=True)  # Nullable for OAuth users
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # OAuth fields
    oauth_provider = Column(String(20), nullable=True)  # "google", "github", etc.
    oauth_id = Column(String(255), nullable=True)

    # Provider settings
    default_provider = Column(String(50), nullable=True)  # Default LLM provider
    use_local_llm = Column(Boolean, default=False)  # Whether to use local LLM

    # Memory service LLM settings
    memory_service_provider = Column(String(50), nullable=True)  # LLM provider for memory service
    memory_service_model = Column(String(100), nullable=True)  # LLM model for memory service

    # Concierge agent LLM settings
    concierge_agent_provider = Column(String(50), nullable=True)  # LLM provider for concierge agent
    concierge_agent_model = Column(String(100), nullable=True)  # LLM model for concierge agent

    # Industry selection for profile templates
    selected_industry = Column(String(100), nullable=True)
    industry_selection_completed = Column(Boolean, default=False)

    # Relationships
    conversations = relationship("Conversation", back_populates="user", cascade="all, delete-orphan")
    files = relationship("File", back_populates="user", cascade="all, delete-orphan")
    tasks = relationship("Task", back_populates="user", cascade="all, delete-orphan")
    provider_api_keys = relationship("ProviderApiKey", back_populates="user", cascade="all, delete-orphan")
    data_sources = relationship("DataSource", back_populates="user", cascade="all, delete-orphan")
    business_profiles = relationship("BusinessProfile", back_populates="user", cascade="all, delete-orphan")
    cart_items = relationship("CartItem", back_populates="user", cascade="all, delete-orphan")
    purchases = relationship("Purchase", back_populates="user", cascade="all, delete-orphan")
    mcp_servers = relationship("MCPServer", back_populates="user", cascade="all, delete-orphan")
    mcp_input_variables = relationship("MCPInputVariable", back_populates="user", cascade="all, delete-orphan")
    subscriptions = relationship("Subscription", back_populates="user", cascade="all, delete-orphan")
    dashboards = relationship("Dashboard", back_populates="user", cascade="all, delete-orphan")
    dashboard_sections = relationship("DashboardSection", back_populates="user", cascade="all, delete-orphan")
    dashboard_widgets = relationship("DashboardWidget", back_populates="user", cascade="all, delete-orphan")
    search_activities = relationship("UserSearchActivity", back_populates="user", cascade="all, delete-orphan")

class Conversation(Base):
    """Model for chat conversations."""
    __tablename__ = "conversations"

    id = Column(String(64), primary_key=True, index=True)  # UUID - increased for hierarchical IDs
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    title = Column(String(255))
    persona_id = Column(String(50), index=True)  # ID of the AI persona
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)
    is_archived = Column(Boolean, default=False)
    state = Column(String(50), nullable=True, index=True) # Added state field for workflow management
    conversation_metadata = Column(JSON, nullable=True)  # For storing additional data

    # Relationships
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    message_threads = relationship("MessageThread", back_populates="conversation", cascade="all, delete-orphan")

class Message(Base):
    """Model for chat messages with hierarchical threading support."""
    __tablename__ = "messages"

    id = Column(String(64), primary_key=True, index=True)  # UUID - increased for hierarchical IDs
    conversation_id = Column(String(64), ForeignKey("conversations.id"), index=True)

    # Hierarchical message threading
    parent_message_id = Column(String(64), ForeignKey("messages.id"), nullable=True, index=True)
    thread_id = Column(String(64), nullable=True, index=True)  # Root message ID for the thread
    message_sequence = Column(Integer, default=0, index=True)  # Sequence number within conversation
    thread_sequence = Column(Integer, default=0, index=True)  # Sequence number within thread

    # Message content and metadata
    sender = Column(String(10))  # "user" or "ai"
    content = Column(Text)
    message_metadata = Column(JSON, nullable=True)  # For storing additional data like visualizations

    # Message state for editing and reprocessing
    is_edited = Column(Boolean, default=False)
    original_message_id = Column(String(64), nullable=True, index=True)  # Reference to original if this is an edit
    edit_timestamp = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    parent_message = relationship("Message", remote_side=[id], backref="child_messages")

    def __repr__(self):
        return f"<Message(id={self.id}, conversation_id={self.conversation_id}, sender={self.sender}, sequence={self.message_sequence})>"

class File(Base):
    """Model for uploaded files."""
    __tablename__ = "files"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    filename = Column(String(255))
    file_path = Column(String(255))
    file_size = Column(Integer)
    num_rows = Column(Integer, nullable=True)  # For CSV/Excel files
    columns = Column(JSON, nullable=True)  # For CSV/Excel files
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="files")
    tasks = relationship("Task", back_populates="input_file", cascade="all, delete-orphan")

class Task(Base):
    """Model for background tasks."""
    __tablename__ = "tasks"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    task_type = Column(String(50))  # "classification", "analysis", etc.
    status = Column(String(20))  # "pending", "running", "completed", "failed"
    message = Column(Text, nullable=True)  # Status message or error message
    input_file_id = Column(String(36), ForeignKey("files.id"), nullable=True)
    result_file_path = Column(String(255), nullable=True)
    config = Column(JSON, nullable=True)  # Task configuration
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="tasks")
    input_file = relationship("File", back_populates="tasks")

class ProviderApiKey(Base):
    """Model for provider API keys."""
    __tablename__ = "provider_api_keys"

    id = Column(Integer, primary_key=True, index=True)
    provider_id = Column(String(50), nullable=False, index=True)
    api_key = Column(String(255), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Unique constraint
    __table_args__ = (UniqueConstraint('provider_id', 'user_id', name='uix_provider_user'),)

    # Relationships
    user = relationship("User", back_populates="provider_api_keys")

class BusinessProfile(Base):
    """Model for business profiles."""
    __tablename__ = "business_profiles"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    # Business context fields (from marketing form)
    industry = Column(String(100), nullable=True)
    business_type = Column(String(50), nullable=True)  # B2B, B2C, B2B2C
    business_size = Column(String(50), nullable=True)  # startup, small, medium, large
    target_audience = Column(Text, nullable=True)
    products_services = Column(Text, nullable=True)
    marketing_goals = Column(Text, nullable=True)
    competitive_landscape = Column(Text, nullable=True)
    budget_indicators = Column(String(100), nullable=True)
    geographic_focus = Column(String(255), nullable=True)
    business_stage = Column(String(50), nullable=True)

    # Marketing-specific fields (consolidated from marketing form)
    budget = Column(Text, nullable=True)  # Budget constraints, allocations, or financial considerations
    timeline = Column(Text, nullable=True)  # Timeline constraints, deadlines, or scheduling requirements
    platforms = Column(Text, nullable=True)  # Specific platforms, channels, or mediums for content distribution

    # Profile management
    is_active = Column(Boolean, default=False)  # Currently selected profile
    knowledge_graph_id = Column(String(36), nullable=True)  # Separate KG space
    context_metadata = Column(JSON, nullable=True)  # Flexible business context

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="business_profiles")
    data_source_assignments = relationship("BusinessProfileDataSource", back_populates="business_profile", cascade="all, delete-orphan")
    dashboards = relationship("Dashboard", back_populates="business_profile", cascade="all, delete-orphan")
    mcp_servers = relationship("MCPServer", back_populates="business_profile", cascade="all, delete-orphan")

    # Constraints - Only allow one active profile per user
    __table_args__ = ()


class BusinessProfileDataSource(Base):
    """Association model for business profile data sources."""
    __tablename__ = "business_profile_data_sources"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    business_profile_id = Column(String(36), ForeignKey("business_profiles.id"), nullable=False)
    data_source_id = Column(String(36), ForeignKey("data_sources.id"), nullable=False)

    # Data source role in business context
    role = Column(String(100), nullable=True)  # sales_data, business_description, marketing_materials, etc.
    priority = Column(Integer, default=1)  # For context ordering
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    business_profile = relationship("BusinessProfile", back_populates="data_source_assignments")
    data_source = relationship("DataSource", back_populates="business_profile_assignments")

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('business_profile_id', 'data_source_id', name='unique_profile_data_source'),
    )


class DataSource(Base):
    """Model for data sources."""
    __tablename__ = "data_sources"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    name = Column(String(255))
    type = Column(String(20))  # file, database, api, mcp
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    source_metadata = Column(JSON, nullable=True)  # Renamed from metadata to avoid conflict with SQLAlchemy
    user_id = Column(Integer, ForeignKey("users.id"))
    sync_status = Column(String(50), default='active')
    last_sync = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="data_sources")
    dashboards = relationship("Dashboard", secondary="dashboard_data_sources", back_populates="data_sources")
    business_profile_assignments = relationship("BusinessProfileDataSource", back_populates="data_source", cascade="all, delete-orphan")


class MCPServer(Base):
    """Enhanced MCP server configurations."""
    __tablename__ = "mcp_servers"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    business_profile_id = Column(String(36), ForeignKey("business_profiles.id"), nullable=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    config_type = Column(String(20), default='json')  # 'json', 'form', 'auto'
    transport_type = Column(String(20), default='http')  # 'http', 'stdio', 'sse', 'ws'
    configuration = Column(JSON, nullable=False)
    status = Column(String(20), default='inactive')  # 'active', 'inactive', 'error'
    last_connected_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="mcp_servers")
    business_profile = relationship("BusinessProfile", back_populates="mcp_servers")
    tools = relationship("MCPTool", back_populates="server", cascade="all, delete-orphan")
    resources = relationship("MCPResource", back_populates="server", cascade="all, delete-orphan")
    prompts = relationship("MCPPrompt", back_populates="server", cascade="all, delete-orphan")


class MCPTool(Base):
    """MCP server tools registry."""
    __tablename__ = "mcp_tools"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    server_id = Column(String(36), ForeignKey("mcp_servers.id", ondelete="CASCADE"), nullable=False)
    tool_name = Column(String(100), nullable=False)
    tool_description = Column(Text)
    parameters = Column(JSON)
    capabilities = Column(JSON)
    is_enabled = Column(Boolean, default=True)
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    server = relationship("MCPServer", back_populates="tools")

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('server_id', 'tool_name', name='unique_server_tool'),
    )


class MCPResource(Base):
    """MCP server resources."""
    __tablename__ = "mcp_resources"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    server_id = Column(String(36), ForeignKey("mcp_servers.id", ondelete="CASCADE"), nullable=False)
    resource_type = Column(String(100), nullable=False)
    resource_name = Column(String(100), nullable=False)
    resource_description = Column(Text)
    parameters = Column(JSON)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    server = relationship("MCPServer", back_populates="resources")

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('server_id', 'resource_name', name='unique_server_resource'),
    )


class MCPPrompt(Base):
    """MCP server prompts."""
    __tablename__ = "mcp_prompts"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    server_id = Column(String(36), ForeignKey("mcp_servers.id", ondelete="CASCADE"), nullable=False)
    prompt_name = Column(String(100), nullable=False)
    prompt_description = Column(Text)
    template = Column(Text, nullable=False)
    parameters = Column(JSON)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    server = relationship("MCPServer", back_populates="prompts")

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('server_id', 'prompt_name', name='unique_server_prompt'),
    )


class MCPInputVariable(Base):
    """Input variables for secure credential management."""
    __tablename__ = "mcp_input_variables"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    variable_id = Column(String(100), nullable=False)
    variable_type = Column(String(20), nullable=False)  # 'promptString', 'promptChoice', 'env'
    description = Column(Text)
    is_password = Column(Boolean, default=False)
    encrypted_value = Column(Text)  # Encrypted storage
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="mcp_input_variables")

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('user_id', 'variable_id', name='unique_user_variable'),
    )


class AgentInsight(Base):
    """Model for agent insights in cross-agent intelligence system."""
    __tablename__ = "agent_insights"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    business_profile_id = Column(String(36), ForeignKey("business_profiles.id"), nullable=False, index=True)
    source_agent_id = Column(String(100), nullable=False, index=True)
    insight_type = Column(String(50), nullable=False)  # marketing_strategy, data_insights, etc.
    content = Column(Text, nullable=False)
    insight_metadata = Column(JSON, default=lambda: {})
    relevance_tags = Column(JSON, default=lambda: [])  # Tags for relevance matching
    confidence_score = Column(Float, default=1.0)
    access_count = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)
    last_accessed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    business_profile = relationship("BusinessProfile")

    # Indexes
    __table_args__ = (
        Index('idx_agent_insights_profile_agent', 'business_profile_id', 'source_agent_id'),
        Index('idx_agent_insights_type', 'insight_type'),
        Index('idx_agent_insights_created', 'created_at'),
    )


class AgentInteraction(Base):
    """Model for agent interactions in cross-agent intelligence system."""
    __tablename__ = "agent_interactions"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    business_profile_id = Column(String(36), ForeignKey("business_profiles.id"), nullable=False, index=True)
    agent_id = Column(String(100), nullable=False, index=True)
    user_message = Column(Text, nullable=True)
    agent_response = Column(Text, nullable=True)
    context_used = Column(JSON, default=lambda: [])  # Context items used in response
    tools_used = Column(JSON, default=lambda: [])  # Tools used in interaction
    insights_generated = Column(JSON, default=lambda: [])  # Insights generated from interaction
    outcome = Column(String(50), default='unknown')  # success, error, partial, etc.
    interaction_metadata = Column(JSON, default=lambda: {})

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    business_profile = relationship("BusinessProfile")

    # Indexes
    __table_args__ = (
        Index('idx_agent_interactions_profile_agent', 'business_profile_id', 'agent_id'),
        Index('idx_agent_interactions_created', 'created_at'),
    )


class SharedContext(Base):
    """Model for shared context between agents."""
    __tablename__ = "shared_contexts"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    business_profile_id = Column(String(36), ForeignKey("business_profiles.id"), nullable=False, index=True)
    source_agent_id = Column(String(100), nullable=False)
    target_agent_id = Column(String(100), nullable=False)
    context_type = Column(String(50), nullable=False)  # handoff, collaboration, insight_sharing
    context_data = Column(JSON, nullable=False)
    context_metadata = Column(JSON, default=lambda: {})
    is_consumed = Column(Boolean, default=False)  # Whether target agent has consumed this context
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    business_profile = relationship("BusinessProfile")

    # Indexes
    __table_args__ = (
        Index('idx_shared_contexts_target', 'business_profile_id', 'target_agent_id'),
        Index('idx_shared_contexts_source', 'business_profile_id', 'source_agent_id'),
        Index('idx_shared_contexts_expires', 'expires_at'),
    )

class CartItem(Base):
    """Model for cart items."""
    __tablename__ = "cart_items"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    persona_id = Column(String(50), index=True)  # ID of the AI persona
    quantity = Column(Integer, default=1)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="cart_items")

    # Unique constraint to prevent duplicate items
    __table_args__ = (UniqueConstraint('user_id', 'persona_id', name='_user_persona_uc'),)

class Purchase(Base):
    """Model for purchases."""
    __tablename__ = "purchases"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    total_amount = Column(Float)
    payment_status = Column(String(20))  # "pending", "completed", "failed"
    payment_method = Column(String(50), nullable=True)
    purchase_metadata = Column(JSON, nullable=True)  # For storing additional data
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="purchases")
    items = relationship("PurchasedItem", back_populates="purchase", cascade="all, delete-orphan")

class PurchasedItem(Base):
    """Model for purchased items."""
    __tablename__ = "purchased_items"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    purchase_id = Column(String(36), ForeignKey("purchases.id"), index=True)
    persona_id = Column(String(50), index=True)  # ID of the AI persona
    quantity = Column(Integer, default=1)
    price = Column(Float)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    purchase = relationship("Purchase", back_populates="items")


class Persona(Base):
    """Model for AI personas."""
    __tablename__ = "personas"

    id = Column(String(50), primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    industry = Column(String(50))
    skills = Column(JSON)
    rating = Column(Float, default=4.5)
    review_count = Column(Integer, default=0)
    image_url = Column(String(255), default="/placeholder.svg")
    price = Column(Float, default=10.0)
    provider = Column(String(50), default="groq")
    model = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    age_restriction = Column(Integer, default=0)
    content_filters = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    versions = relationship("PersonaVersion", back_populates="persona", cascade="all, delete-orphan")


class PersonaVersion(Base):
    """Model for persona versions."""
    __tablename__ = "persona_versions"

    id = Column(String(100), primary_key=True, index=True)  # Format: {persona_id}-{version}
    persona_id = Column(String(50), ForeignKey("personas.id"), index=True)
    version = Column(String(20), nullable=False)  # Semantic version (e.g., "1.0.0")
    config = Column(JSON, nullable=False)  # Full configuration for this version
    is_active = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    created_by = Column(Integer, ForeignKey("users.id"))

    # Relationships
    persona = relationship("Persona", back_populates="versions")
    creator = relationship("User")


class Agent(Base):
    """Model for AI agents."""
    __tablename__ = "agents"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    agent_type = Column(String(50), nullable=False)  # e.g., "persona", "workflow", "custom"
    persona_id = Column(String(50), ForeignKey("personas.id"), nullable=True, index=True)
    configuration = Column(JSON, nullable=True)  # Agent-specific configuration
    capabilities = Column(JSON, nullable=True)  # List of capabilities
    status = Column(String(20), default="idle")  # idle, active, busy, error, inactive
    is_active = Column(Boolean, default=True)
    execution_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    metadata = Column(JSON, nullable=True)  # Additional metadata
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="agents")
    persona = relationship("Persona")


class AdminActivityLog(Base):
    """Model for admin activity logs."""
    __tablename__ = "admin_activity_logs"

    id = Column(Integer, primary_key=True, index=True)
    admin_id = Column(Integer, ForeignKey("users.id"), index=True)
    action = Column(String(100), nullable=False)
    entity_type = Column(String(50), nullable=False)
    entity_id = Column(String(50), nullable=True)
    details = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    admin = relationship("User")

# Database functions
def init_db():
    """Initialize the database."""
    Base.metadata.create_all(bind=engine)

def get_db():
    """Get a database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# User functions
def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Get a user by email."""
    return db.query(User).filter(User.email == email).first()

def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    """Get a user by ID."""
    return db.query(User).filter(User.id == user_id).first()

def get_user_by_oauth(db: Session, provider: str, oauth_id: str) -> Optional[User]:
    """Get a user by OAuth provider and ID."""
    return db.query(User).filter(
        User.oauth_provider == provider,
        User.oauth_id == oauth_id
    ).first()

# Conversation functions
def create_conversation(db: Session, user_id: int, title: str, persona_id: str, metadata: Optional[Dict[str, Any]] = None) -> Conversation:
    """Create a new conversation."""
    logger.debug(f"Entering create_conversation for user {user_id}, title: {title}, persona: {persona_id}")
    logger.info(f"✅ DATABASE: Creating conversation with persona_id: {persona_id}")
    conversation = Conversation(
        id=str(uuid.uuid4()),
        user_id=user_id,
        title=title,
        persona_id=persona_id,
        conversation_metadata=metadata,
        state=None # Explicitly set initial state if needed, though model has no default
    )
    logger.debug(f"Conversation object created with ID: {conversation.id}, state: {conversation.state}")
    try:
        logger.debug(f"Attempting to add conversation {conversation.id} to session.")
        db.add(conversation)
        logger.debug(f"Attempting to commit session for conversation {conversation.id}.")
        db.commit()
        logger.debug(f"Commit successful for conversation {conversation.id}.")
        db.refresh(conversation)
        logger.debug(f"Conversation {conversation.id} refreshed.")
        logger.info(f"✅ DATABASE: Successfully created conversation {conversation.id} with persona_id: {conversation.persona_id}")
        return conversation
    except Exception as e:
        logger.error(f"Error during DB commit/refresh in create_conversation for ID {conversation.id}: {str(e)}", exc_info=True)
        db.rollback() # Rollback the transaction on error
        raise # Re-raise the exception to be handled by the caller

def get_conversation(db: Session, conversation_id: str) -> Optional[Conversation]:
    """Get a conversation by ID."""
    return db.query(Conversation).filter(Conversation.id == conversation_id).first()

def get_user_conversations(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get all conversations for a user."""
    return db.query(Conversation).filter(
        Conversation.user_id == user_id,
        Conversation.is_archived == False
    ).order_by(Conversation.updated_at.desc()).offset(skip).limit(limit).all()

def update_conversation(db: Session, conversation_id: str, update_data: Optional[Dict[str, Any]] = None, title: Optional[str] = None, is_archived: Optional[bool] = None, metadata: Optional[Dict[str, Any]] = None) -> Optional[Conversation]:
    """Update a conversation.

    Args:
        db: Database session
        conversation_id: ID of the conversation to update
        update_data: Dictionary containing update data (can include title, is_archived, metadata)
        title: New title for the conversation (deprecated, use update_data instead)
        is_archived: Whether the conversation is archived (deprecated, use update_data instead)
        metadata: New metadata for the conversation (deprecated, use update_data instead)

    Returns:
        Updated conversation or None if not found
    """
    logger.debug(f"Updating conversation {conversation_id}")
    logger.debug(f"update_data: {update_data}, title: {title}, is_archived: {is_archived}")

    conversation = get_conversation(db, conversation_id)
    if not conversation:
        logger.warning(f"Conversation {conversation_id} not found")
        return None

    # Handle the update_data dictionary if provided
    if update_data is not None:
        logger.debug(f"Processing update_data: {update_data}")

        if 'title' in update_data and update_data['title'] is not None:
            # Ensure title is a string
            if isinstance(update_data['title'], dict):
                # If title is a dict, log warning and don't update
                logger.warning(f"Received dict for title in update_conversation: {update_data['title']}")
            else:
                conversation.title = update_data['title']
                logger.debug(f"Updated title to: {update_data['title']}")

        if 'is_archived' in update_data and update_data['is_archived'] is not None:
            conversation.is_archived = update_data['is_archived']
            logger.debug(f"Updated is_archived to: {update_data['is_archived']}")

        if 'metadata' in update_data and update_data['metadata'] is not None:
            # Sanitize metadata to ensure it's JSON serializable
            sanitized_metadata = sanitize_json(update_data['metadata'])
            conversation.conversation_metadata = sanitized_metadata
            logger.debug(f"Updated metadata to: {sanitized_metadata}")

        if 'persona_id' in update_data and update_data['persona_id'] is not None:
            old_persona_id = conversation.persona_id
            conversation.persona_id = update_data['persona_id']
            logger.info(f"✅ DATABASE: Updated conversation {conversation_id} persona_id from {old_persona_id} to {update_data['persona_id']}")

        if 'state' in update_data and update_data['state'] is not None:
            conversation.state = update_data['state']
            logger.debug(f"Updated state to: {update_data['state']}")

    # Handle individual parameters for backward compatibility
    if title is not None:
        conversation.title = title
        logger.debug(f"Updated title to: {title}")
    if is_archived is not None:
        conversation.is_archived = is_archived
        logger.debug(f"Updated is_archived to: {is_archived}")
    if metadata is not None:
        # Sanitize metadata to ensure it's JSON serializable
        sanitized_metadata = sanitize_json(metadata)
        conversation.conversation_metadata = sanitized_metadata
        logger.debug(f"Updated metadata to: {sanitized_metadata}")

    db.commit()
    db.refresh(conversation)
    return conversation

def delete_conversation(db: Session, conversation_id: str) -> bool:
    """Delete a conversation."""
    conversation = get_conversation(db, conversation_id)
    if not conversation:
        return False

    db.delete(conversation)
    db.commit()
    return True

# Message functions
def create_message(
    db: Session,
    conversation_id: str,
    sender: str,
    content: str,
    metadata: Optional[Dict[str, Any]] = None,
    parent_message_id: Optional[str] = None,
    thread_id: Optional[str] = None
) -> Message:
    """Create a new message with hierarchical threading support."""
    # Sanitize metadata to ensure it's JSON serializable
    sanitized_metadata = sanitize_json(metadata)

    # Generate hierarchical ID
    message_id = _generate_hierarchical_message_id(conversation_id, parent_message_id)

    # Get next sequence numbers
    message_sequence = _get_next_message_sequence(db, conversation_id)
    thread_sequence = 0

    # Determine thread_id and thread_sequence
    if parent_message_id:
        parent_message = get_message(db, parent_message_id)
        if parent_message:
            thread_id = parent_message.thread_id or parent_message.id
            thread_sequence = _get_next_thread_sequence(db, thread_id)
    else:
        # This is a root message, so it starts its own thread
        thread_id = message_id

    message = Message(
        id=message_id,
        conversation_id=conversation_id,
        parent_message_id=parent_message_id,
        thread_id=thread_id,
        message_sequence=message_sequence,
        thread_sequence=thread_sequence,
        sender=sender,
        content=content,
        message_metadata=sanitized_metadata
    )
    db.add(message)
    db.commit()
    db.refresh(message)
    return message

def _generate_hierarchical_message_id(conversation_id: str, parent_message_id: Optional[str] = None) -> str:
    """Generate a hierarchical message ID based on conversation and parent message."""
    base_uuid = str(uuid.uuid4())

    if parent_message_id:
        # For child messages, create a hierarchical ID that shows the relationship
        # Format: conversation_prefix-parent_prefix-new_uuid
        conversation_prefix = conversation_id[:8]
        parent_prefix = parent_message_id[:8]
        return f"{conversation_prefix}-{parent_prefix}-{base_uuid}"
    else:
        # For root messages, use conversation prefix
        conversation_prefix = conversation_id[:8]
        return f"{conversation_prefix}-{base_uuid}"

def _get_next_message_sequence(db: Session, conversation_id: str) -> int:
    """Get the next message sequence number for a conversation."""
    try:
        max_sequence = db.query(func.max(Message.message_sequence)).filter(
            Message.conversation_id == conversation_id
        ).scalar()
        return (max_sequence or 0) + 1
    except Exception as e:
        logger.error(f"Error getting next message sequence: {e}")
        return 1

def _get_next_thread_sequence(db: Session, thread_id: str) -> int:
    """Get the next thread sequence number for a message thread."""
    try:
        max_sequence = db.query(func.max(Message.thread_sequence)).filter(
            Message.thread_id == thread_id
        ).scalar()
        return (max_sequence or 0) + 1
    except Exception as e:
        logger.error(f"Error getting next thread sequence: {e}")
        return 1

def get_message(db: Session, message_id: str) -> Optional[Message]:
    """Get a message by ID."""
    return db.query(Message).filter(Message.id == message_id).first()

def get_conversation_messages(db: Session, conversation_id: str, skip: int = 0, limit: int = 100) -> List[Message]:
    """Get all messages for a conversation ordered by sequence."""
    return db.query(Message).filter(
        Message.conversation_id == conversation_id
    ).order_by(Message.message_sequence.asc()).offset(skip).limit(limit).all()

def get_message_thread(db: Session, thread_id: str) -> List[Message]:
    """Get all messages in a thread ordered by thread sequence."""
    return db.query(Message).filter(
        Message.thread_id == thread_id
    ).order_by(Message.thread_sequence.asc()).all()

def get_message_children(db: Session, parent_message_id: str) -> List[Message]:
    """Get all direct child messages of a parent message."""
    return db.query(Message).filter(
        Message.parent_message_id == parent_message_id
    ).order_by(Message.thread_sequence.asc()).all()

def create_message_edit(
    db: Session,
    original_message_id: str,
    new_content: str,
    metadata: Optional[Dict[str, Any]] = None
) -> Message:
    """Create an edited version of a message."""
    original_message = get_message(db, original_message_id)
    if not original_message:
        raise ValueError(f"Original message {original_message_id} not found")

    # Sanitize metadata
    sanitized_metadata = sanitize_json(metadata or {})

    # Create the edited message as a child of the original
    edited_message = create_message(
        db=db,
        conversation_id=original_message.conversation_id,
        sender=original_message.sender,
        content=new_content,
        metadata=sanitized_metadata,
        parent_message_id=original_message_id,
        thread_id=original_message.thread_id
    )

    # Mark as edited and set references
    edited_message.is_edited = True
    edited_message.original_message_id = original_message_id
    edited_message.edit_timestamp = get_utc_now()

    db.commit()
    db.refresh(edited_message)
    return edited_message

def get_message_edit_history(db: Session, original_message_id: str) -> List[Message]:
    """Get the edit history for a message."""
    return db.query(Message).filter(
        Message.original_message_id == original_message_id
    ).order_by(Message.created_at.asc()).all()

def update_message(db: Session, message_id: str, content: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> Optional[Message]:
    """Update a message."""
    logger.debug(f"🔄 Attempting to update message {message_id}")
    message = db.query(Message).filter(Message.id == message_id).first()
    if not message:
        logger.error(f"❌ Message {message_id} not found in database")
        return None

    logger.info(f"📝 Found message {message_id}: sender={message.sender}, current_content='{message.content[:50]}...'")

    if content is not None:
        logger.info(f"📝 Updating content from '{message.content[:50]}...' to '{content[:50]}...'")
        message.content = content
    if metadata is not None:
        # Sanitize metadata to ensure it's JSON serializable
        sanitized_metadata = sanitize_json(metadata)
        logger.info(f"📝 Updating metadata: {sanitized_metadata}")
        message.message_metadata = sanitized_metadata

    try:
        db.commit()
        db.refresh(message)
        logger.info(f"✅ Successfully updated message {message_id}: sender={message.sender}, content_length={len(message.content)}")
        return message
    except Exception as e:
        logger.error(f"❌ Failed to commit message update for {message_id}: {str(e)}", exc_info=True)
        db.rollback()
        return None

# File functions
def create_file(db: Session, user_id: int, filename: str, file_path: str, file_size: int, num_rows: Optional[int] = None, columns: Optional[List[str]] = None) -> File:
    """Create a new file."""
    file = File(
        id=str(uuid.uuid4()),
        user_id=user_id,
        filename=filename,
        file_path=file_path,
        file_size=file_size,
        num_rows=num_rows,
        columns=columns
    )
    db.add(file)
    db.commit()
    db.refresh(file)
    return file

def get_file(db: Session, file_id: str) -> Optional[File]:
    """Get a file by ID."""
    return db.query(File).filter(File.id == file_id).first()

def get_user_files(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[File]:
    """Get all files for a user."""
    return db.query(File).filter(
        File.user_id == user_id
    ).order_by(File.created_at.desc()).offset(skip).limit(limit).all()

def delete_file(db: Session, file_id: str) -> bool:
    """Delete a file."""
    file = get_file(db, file_id)
    if not file:
        return False

    db.delete(file)
    db.commit()
    return True

# Task functions
def create_task(db: Session, user_id: int, task_type: str, input_file_id: Optional[str] = None, config: Optional[Dict[str, Any]] = None) -> Task:
    """Create a new task."""
    task = Task(
        id=str(uuid.uuid4()),
        user_id=user_id,
        task_type=task_type,
        status="pending",
        input_file_id=input_file_id,
        config=config
    )
    db.add(task)
    db.commit()
    db.refresh(task)
    return task

def get_task(db: Session, task_id: str) -> Optional[Task]:
    """Get a task by ID."""
    return db.query(Task).filter(Task.id == task_id).first()

def get_user_tasks(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Task]:
    """Get all tasks for a user."""
    return db.query(Task).filter(
        Task.user_id == user_id
    ).order_by(Task.created_at.desc()).offset(skip).limit(limit).all()

def update_task_status(db: Session, task_id: str, status: str, message: Optional[str] = None, result_file_path: Optional[str] = None) -> Optional[Task]:
    """Update a task status."""
    task = get_task(db, task_id)
    if not task:
        return None

    task.status = status
    if message is not None:
        task.message = message
    if result_file_path is not None:
        task.result_file_path = result_file_path
    if status in ["completed", "failed"]:
        task.completed_at = get_utc_now()

    db.commit()
    db.refresh(task)
    return task

# Provider API key functions
def get_provider_api_key(db: Session, user_id: int, provider_id: str) -> Optional[ProviderApiKey]:
    """Get a provider API key for a user."""
    return db.query(ProviderApiKey).filter(
        ProviderApiKey.user_id == user_id,
        ProviderApiKey.provider_id == provider_id
    ).first()

def get_user_provider_api_keys(db: Session, user_id: int) -> List[ProviderApiKey]:
    """Get all provider API keys for a user."""
    return db.query(ProviderApiKey).filter(
        ProviderApiKey.user_id == user_id
    ).all()

def set_provider_api_key(db: Session, user_id: int, provider_id: str, api_key: str) -> ProviderApiKey:
    """Set a provider API key for a user."""
    # Check if the API key already exists
    existing_key = get_provider_api_key(db, user_id, provider_id)
    if existing_key:
        # Update existing key
        existing_key.api_key = api_key
        existing_key.updated_at = get_utc_now()
        db.commit()
        db.refresh(existing_key)
        return existing_key
    else:
        # Create new key
        provider_api_key = ProviderApiKey(
            user_id=user_id,
            provider_id=provider_id,
            api_key=api_key
        )
        db.add(provider_api_key)
        db.commit()
        db.refresh(provider_api_key)
        return provider_api_key

def delete_provider_api_key(db: Session, user_id: int, provider_id: str) -> bool:
    """Delete a provider API key for a user."""
    provider_api_key = get_provider_api_key(db, user_id, provider_id)
    if not provider_api_key:
        return False

    db.delete(provider_api_key)
    db.commit()
    return True

# Data source functions
def create_data_source(db: Session, user_id: int, data_source_data: Union[FileDataSourceCreate, DatabaseDataSourceCreate, ApiDataSourceCreate, McpDataSourceCreate]) -> DataSource:
    """Create a new data source."""
    data_source = DataSource(
        id=str(uuid.uuid4()),
        user_id=user_id,
        name=data_source_data.name,
        type=data_source_data.type,
        description=data_source_data.description,
        is_active=data_source_data.is_active,
        source_metadata=data_source_data.model_dump(exclude={"name", "type", "description", "is_active"})
    )
    db.add(data_source)
    db.commit()
    db.refresh(data_source)
    return data_source

def get_data_source(db: Session, data_source_id: str) -> Optional[DataSource]:
    """Get a data source by ID."""
    return db.query(DataSource).filter(DataSource.id == data_source_id).first()

def get_user_data_sources(db: Session, user_id: int, skip: int = 0, limit: int = 100, type: Optional[str] = None) -> List[DataSource]:
    """Get data sources for a user."""
    query = db.query(DataSource).filter(DataSource.user_id == user_id)
    if type:
        query = query.filter(DataSource.type == type)
    return query.offset(skip).limit(limit).all()

def update_data_source(db: Session, data_source_id: str, update_data: Dict[str, Any]) -> Optional[DataSource]:
    """Update a data source."""
    data_source = get_data_source(db, data_source_id)
    if not data_source:
        return None

    # Update allowed fields
    allowed_fields = ['name', 'description', 'is_active']
    for key, value in update_data.items():
        if key in allowed_fields:
            setattr(data_source, key, value)

    db.commit()
    db.refresh(data_source)
    return data_source


# Admin functions
def get_users(db: Session, skip: int = 0, limit: int = 100, search: Optional[str] = None, is_active: Optional[bool] = None, is_superuser: Optional[bool] = None) -> Tuple[List[User], int]:
    """Get users with filtering and pagination."""
    query = db.query(User)

    # Apply filters
    if search:
        query = query.filter(
            (User.email.ilike(f"%{search}%")) |
            (User.username.ilike(f"%{search}%"))
        )
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    if is_superuser is not None:
        query = query.filter(User.is_superuser == is_superuser)

    # Get total count
    total = query.count()

    # Apply pagination
    users = query.order_by(User.created_at.desc()).offset(skip).limit(limit).all()

    return users, total


def update_user(db: Session, user_id: int, user_data: Dict[str, Any]) -> Optional[User]:
    """Update a user."""
    user = get_user_by_id(db, user_id)
    if not user:
        return None

    for key, value in user_data.items():
        setattr(user, key, value)

    db.commit()
    db.refresh(user)
    return user


def get_persona(db: Session, persona_id: str) -> Optional[Persona]:
    """Get a persona by ID."""
    return db.query(Persona).filter(Persona.id == persona_id).first()


def get_personas(db: Session, skip: int = 0, limit: int = 100, industry: Optional[str] = None, is_active: Optional[bool] = None) -> List[Persona]:
    """Get personas with filtering and pagination."""
    query = db.query(Persona)

    # Apply filters
    if industry:
        query = query.filter(Persona.industry == industry)
    if is_active is not None:
        query = query.filter(Persona.is_active == is_active)

    # Apply pagination
    personas = query.order_by(Persona.created_at.desc()).offset(skip).limit(limit).all()

    return personas


def create_persona(db: Session, persona_data: Dict[str, Any]) -> Persona:
    """Create a new persona."""
    persona = Persona(**persona_data)
    db.add(persona)
    db.commit()
    db.refresh(persona)
    return persona


def update_persona(db: Session, persona_id: str, persona_data: Dict[str, Any]) -> Optional[Persona]:
    """Update a persona."""
    persona = get_persona(db, persona_id)
    if not persona:
        return None

    for key, value in persona_data.items():
        setattr(persona, key, value)

    db.commit()
    db.refresh(persona)
    return persona


def delete_persona(db: Session, persona_id: str) -> bool:
    """Delete a persona."""
    persona = get_persona(db, persona_id)
    if not persona:
        return False

    db.delete(persona)
    db.commit()
    return True


# Persona version functions
def get_persona_version(db: Session, version_id: str) -> Optional[PersonaVersion]:
    """Get a persona version by ID."""
    return db.query(PersonaVersion).filter(PersonaVersion.id == version_id).first()


def get_persona_versions(db: Session, persona_id: str, skip: int = 0, limit: int = 100) -> List[PersonaVersion]:
    """Get all versions for a persona."""
    return db.query(PersonaVersion).filter(
        PersonaVersion.persona_id == persona_id
    ).order_by(PersonaVersion.created_at.desc()).offset(skip).limit(limit).all()


def get_active_persona_version(db: Session, persona_id: str) -> Optional[PersonaVersion]:
    """Get the active version for a persona."""
    return db.query(PersonaVersion).filter(
        PersonaVersion.persona_id == persona_id,
        PersonaVersion.is_active == True
    ).first()


def create_persona_version(db: Session, version_data: Dict[str, Any]) -> PersonaVersion:
    """Create a new persona version."""
    persona_version = PersonaVersion(**version_data)
    db.add(persona_version)

    # If this version is active, deactivate all other versions
    if persona_version.is_active:
        db.query(PersonaVersion).filter(
            PersonaVersion.persona_id == persona_version.persona_id,
            PersonaVersion.id != persona_version.id
        ).update({"is_active": False})

    db.commit()
    db.refresh(persona_version)
    return persona_version


def activate_persona_version(db: Session, version_id: str) -> Optional[PersonaVersion]:
    """Activate a persona version and deactivate all others."""
    version = get_persona_version(db, version_id)
    if not version:
        return None

    # Deactivate all versions for this persona
    db.query(PersonaVersion).filter(
        PersonaVersion.persona_id == version.persona_id
    ).update({"is_active": False})

    # Activate this version
    version.is_active = True
    db.commit()
    db.refresh(version)
    return version


def delete_persona_version(db: Session, version_id: str) -> bool:
    """Delete a persona version."""
    version = get_persona_version(db, version_id)
    if not version:
        return False

    # Don't allow deleting the active version
    if version.is_active:
        return False

    db.delete(version)
    db.commit()
    return True


def log_admin_activity(db: Session, admin_id: int, action: str, entity_type: str, entity_id: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> AdminActivityLog:
    """Log admin activity."""
    log = AdminActivityLog(
        admin_id=admin_id,
        action=action,
        entity_type=entity_type,
        entity_id=entity_id,
        details=details
    )
    db.add(log)
    db.commit()
    db.refresh(log)
    return log


def get_admin_activity_logs(db: Session, skip: int = 0, limit: int = 100, action: Optional[str] = None, entity_type: Optional[str] = None, admin_id: Optional[int] = None) -> Tuple[List[AdminActivityLog], int]:
    """Get admin activity logs with filtering and pagination."""
    query = db.query(AdminActivityLog)

    # Apply filters
    if action:
        query = query.filter(AdminActivityLog.action == action)
    if entity_type:
        query = query.filter(AdminActivityLog.entity_type == entity_type)
    if admin_id:
        query = query.filter(AdminActivityLog.admin_id == admin_id)

    # Get total count
    total = query.count()

    # Apply pagination
    logs = query.order_by(AdminActivityLog.created_at.desc()).offset(skip).limit(limit).all()

    return logs, total


def get_user_count(db: Session, is_active: Optional[bool] = None) -> int:
    """Get the count of users."""
    query = db.query(User)
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    return query.count()


def get_persona_count(db: Session, is_active: Optional[bool] = None) -> int:
    """Get the count of personas."""
    query = db.query(Persona)
    if is_active is not None:
        query = query.filter(Persona.is_active == is_active)
    return query.count()


def get_purchase_count(db: Session) -> int:
    """Get the count of purchases."""
    return db.query(Purchase).count()


def get_total_revenue(db: Session) -> float:
    """Get the total revenue."""
    result = db.query(func.sum(Purchase.total_amount)).filter(Purchase.payment_status == "completed").scalar()
    return result or 0.0


def get_recent_purchases(db: Session, since_date: datetime) -> int:
    """Get the count of recent purchases."""
    return db.query(Purchase).filter(Purchase.created_at >= since_date).count()


def get_recent_revenue(db: Session, since_date: datetime) -> float:
    """Get the recent revenue."""
    result = db.query(func.sum(Purchase.total_amount)).filter(
        Purchase.payment_status == "completed",
        Purchase.created_at >= since_date
    ).scalar()
    return result or 0.0




def get_data_sources_by_file_id(db: Session, file_id: str, user_id: int) -> List[DataSource]:
    """Get data sources that use a specific file."""
    return db.query(DataSource).filter(
        DataSource.user_id == user_id,
        DataSource.type == "file",
        DataSource.source_metadata.op('->>')('file_id') == file_id
    ).all()

def delete_data_source(db: Session, data_source_id: str) -> None:
    """Delete a data source."""
    db.query(DataSource).filter(DataSource.id == data_source_id).delete()
    db.commit()

# Cart functions
def add_to_cart(db: Session, user_id: int, persona_id: str, quantity: int = 1) -> CartItem:
    """Add an item to the cart."""
    # Check if the item already exists in the cart
    cart_item = db.query(CartItem).filter(
        CartItem.user_id == user_id,
        CartItem.persona_id == persona_id
    ).first()

    if cart_item:
        # Update quantity
        cart_item.quantity += quantity
        db.commit()
        db.refresh(cart_item)
        return cart_item
    else:
        # Create new cart item
        cart_item = CartItem(
            id=str(uuid.uuid4()),
            user_id=user_id,
            persona_id=persona_id,
            quantity=quantity
        )
        db.add(cart_item)
        db.commit()
        db.refresh(cart_item)
        return cart_item

def get_cart_items(db: Session, user_id: int) -> List[CartItem]:
    """Get all cart items for a user."""
    return db.query(CartItem).filter(CartItem.user_id == user_id).all()

def get_cart_item(db: Session, cart_item_id: str) -> Optional[CartItem]:
    """Get a cart item by ID."""
    return db.query(CartItem).filter(CartItem.id == cart_item_id).first()

def update_cart_item_quantity(db: Session, cart_item_id: str, quantity: int) -> Optional[CartItem]:
    """Update a cart item quantity."""
    cart_item = get_cart_item(db, cart_item_id)
    if not cart_item:
        return None

    cart_item.quantity = quantity
    db.commit()
    db.refresh(cart_item)
    return cart_item

def remove_from_cart(db: Session, cart_item_id: str) -> bool:
    """Remove an item from the cart."""
    cart_item = get_cart_item(db, cart_item_id)
    if not cart_item:
        return False

    db.delete(cart_item)
    db.commit()
    return True

def clear_cart(db: Session, user_id: int) -> bool:
    """Clear all items from a user's cart."""
    db.query(CartItem).filter(CartItem.user_id == user_id).delete()
    db.commit()
    return True

# Purchase functions
def create_purchase(db: Session, user_id: int, payment_method: str, total_amount: float, metadata: Optional[Dict[str, Any]] = None) -> Purchase:
    """Create a new purchase."""
    purchase = Purchase(
        id=str(uuid.uuid4()),
        user_id=user_id,
        total_amount=total_amount,
        payment_status="pending",
        payment_method=payment_method,
        purchase_metadata=metadata
    )
    db.add(purchase)
    db.commit()
    db.refresh(purchase)
    return purchase

def add_purchased_item(db: Session, purchase_id: str, persona_id: str, quantity: int, price: float) -> PurchasedItem:
    """Add an item to a purchase."""
    purchased_item = PurchasedItem(
        id=str(uuid.uuid4()),
        purchase_id=purchase_id,
        persona_id=persona_id,
        quantity=quantity,
        price=price
    )
    db.add(purchased_item)
    db.commit()
    db.refresh(purchased_item)
    return purchased_item

def get_purchase(db: Session, purchase_id: str) -> Optional[Purchase]:
    """Get a purchase by ID."""
    return db.query(Purchase).filter(Purchase.id == purchase_id).first()

def get_user_purchases(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Purchase]:
    """Get all purchases for a user."""
    return db.query(Purchase).filter(
        Purchase.user_id == user_id
    ).order_by(Purchase.created_at.desc()).offset(skip).limit(limit).all()

def update_purchase_status(db: Session, purchase_id: str, status: str) -> Optional[Purchase]:
    """Update a purchase status."""
    purchase = get_purchase(db, purchase_id)
    if not purchase:
        return None

    purchase.payment_status = status
    db.commit()
    db.refresh(purchase)
    return purchase

def has_user_purchased_persona(db: Session, user_id: int, persona_id: str) -> bool:
    """Check if a user has purchased a specific persona."""
    # Query for completed purchases containing this persona
    purchased_item = db.query(PurchasedItem).join(Purchase).filter(
        Purchase.user_id == user_id,
        Purchase.payment_status == "completed",
        PurchasedItem.persona_id == persona_id
    ).first()

    return purchased_item is not None

def get_user_purchased_personas(db: Session, user_id: int) -> List[str]:
    """Get a list of persona IDs that the user has purchased."""
    # Query for all completed purchases
    purchased_items = db.query(PurchasedItem).join(Purchase).filter(
        Purchase.user_id == user_id,
        Purchase.payment_status == "completed"
    ).all()

    # Extract unique persona IDs
    persona_ids = set(item.persona_id for item in purchased_items)
    return list(persona_ids)

# Provider settings functions
def get_provider_settings(db: Session, user_id: int) -> Dict[str, Any]:
    """Get provider settings for a user."""
    logger.info(f"Getting provider settings for user {user_id}")
    try:
        user = get_user_by_id(db, user_id)
        logger.info(f"User found: {user is not None}")

        if not user:
            logger.warning(f"User {user_id} not found, returning default settings")
            return {
                "default_provider": "",
                "use_local_llm": False,
                "memory_service_provider": "",
                "memory_service_model": "",
                "concierge_agent_provider": "",
                "concierge_agent_model": ""
            }

        # Get the values with fallbacks
        default_provider = user.default_provider or ""
        use_local_llm = user.use_local_llm if user.use_local_llm is not None else False
        memory_service_provider = user.memory_service_provider or ""
        memory_service_model = user.memory_service_model or ""
        concierge_agent_provider = user.concierge_agent_provider or ""
        concierge_agent_model = user.concierge_agent_model or ""

        logger.info(f"Raw provider settings for user {user_id}: default_provider={default_provider}, use_local_llm={use_local_llm}, memory_service_provider={memory_service_provider}, memory_service_model={memory_service_model}, concierge_agent_provider={concierge_agent_provider}, concierge_agent_model={concierge_agent_model}")

        settings = {
            "default_provider": default_provider,
            "use_local_llm": use_local_llm,
            "memory_service_provider": memory_service_provider,
            "memory_service_model": memory_service_model,
            "concierge_agent_provider": concierge_agent_provider,
            "concierge_agent_model": concierge_agent_model
        }

        logger.info(f"Returning provider settings for user {user_id}: {settings}")
        return settings
    except Exception as e:
        logger.error(f"Error getting provider settings for user {user_id}: {str(e)}", exc_info=True)
        return {
            "default_provider": "",
            "use_local_llm": False,
            "memory_service_provider": "",
            "memory_service_model": "",
            "concierge_agent_provider": "",
            "concierge_agent_model": ""
        }

def set_provider_settings(db: Session, user_id: int, settings: Dict[str, Any]) -> bool:
    """Set provider settings for a user."""
    logger.info(f"Setting provider settings for user {user_id}: {settings}")
    try:
        user = get_user_by_id(db, user_id)
        logger.info(f"User found: {user is not None}")

        if not user:
            logger.warning(f"User {user_id} not found, cannot set provider settings")
            return False

        # Update settings
        if "default_provider" in settings:
            # Handle None values for default_provider
            provider_value = settings["default_provider"] if settings["default_provider"] is not None else ""

            # Only update if the value is not empty or if we're explicitly setting it to empty
            if provider_value != "" or ("default_provider" in settings and settings["default_provider"] == ""):
                logger.info(f"Setting default_provider for user {user_id} to '{provider_value}'")
                user.default_provider = provider_value
            else:
                logger.info(f"Not overwriting default_provider for user {user_id} with empty value")

        if "use_local_llm" in settings:
            logger.info(f"Setting use_local_llm for user {user_id} to {settings['use_local_llm']}")
            user.use_local_llm = settings["use_local_llm"]

        if "memory_service_provider" in settings:
            provider_value = settings["memory_service_provider"] if settings["memory_service_provider"] is not None else ""
            logger.info(f"Setting memory_service_provider for user {user_id} to '{provider_value}'")
            user.memory_service_provider = provider_value

        if "memory_service_model" in settings:
            model_value = settings["memory_service_model"] if settings["memory_service_model"] is not None else ""
            logger.info(f"Setting memory_service_model for user {user_id} to '{model_value}'")
            user.memory_service_model = model_value

        if "concierge_agent_provider" in settings:
            provider_value = settings["concierge_agent_provider"] if settings["concierge_agent_provider"] is not None else ""
            logger.info(f"Setting concierge_agent_provider for user {user_id} to '{provider_value}'")
            user.concierge_agent_provider = provider_value

        if "concierge_agent_model" in settings:
            model_value = settings["concierge_agent_model"] if settings["concierge_agent_model"] is not None else ""
            logger.info(f"Setting concierge_agent_model for user {user_id} to '{model_value}'")
            user.concierge_agent_model = model_value

        # Commit changes
        logger.info(f"Committing provider settings changes for user {user_id}")
        db.commit()
        logger.info(f"Provider settings updated successfully for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Error setting provider settings for user {user_id}: {str(e)}", exc_info=True)
        return False
